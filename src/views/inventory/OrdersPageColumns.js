import { h } from 'vue'
import { NIcon, NTag } from 'naive-ui'
import { orderStatusUtils } from '@/utils/dictUtils'

/**
 * 创建订单列表表格列配置
 * @param {Object} icons - 图标对象
 * @param {Object} handlers - 事件处理函数对象
 * @returns {Array} 表格列配置数组
 */
export function createOrdersTableColumns(icons, handlers) {
  const {
    CopyOutlineIcon,
    EyeOutlineIcon,
    CreateOutlineIcon,
    CheckmarkCircleOutlineIcon,
    CloseCircleOutlineIcon,
    ArchiveOutlineIcon,
    CarOutlineIcon
  } = icons

  const {
    copyToClipboard,
    handleView,
    handleEdit,
    handleStatusChange
  } = handlers

  return [
    { type: 'selection', width: 50, fixed: 'left' },

    {
      title: '订单编号',
      key: 'orderSn',
      width: 230,
      fixed: 'left',
      render(row) {
        return h('div', {
          style: {
            display: 'flex',
            alignItems: 'center',
            cursor: 'pointer'
          },
          onClick: () => copyToClipboard(row.orderSn),
          title: '点击复制订单编号'
        }, [
          h(NIcon, {
            size: 16,
            color: 'var(--primary-color)',
            style: {
              opacity: 0.8,
              transition: 'opacity 0.2s'
            },
            onMouseover: (e) => {
              e.target.style.opacity = 1
            },
            onMouseout: (e) => {
              e.target.style.opacity = 0.8
            }
          }, { default: () => h(CopyOutlineIcon) }),
          h('span', {
            style: {
              marginLeft: '5px',
              fontFamily: 'CustomChinese',
              letterSpacing: '0.5px',
              fontWeight: 600
            }
          }, row.orderSn),
        ])
      }
    },

    {
      title: '销售日期',
      key: 'dealDate',
      width: 120,
      render(row) {
        if (!row.dealDate) return '未设置';
        const date = new Date(row.dealDate);
        return date.toLocaleDateString('zh-CN');
      }
    },

    {
      title: '订单类型',
      key: 'orderType',
      width: 100,
      render(row) {
        const typeMap = {
          'deposit': { text: '定金订单', type: 'info', color: '#2080f0' },
          'normal': { text: '销售订单', type: 'success', color: '#18a058' }
        }

        // 默认为销售订单
        const type = typeMap[row.orderType] || typeMap['normal']

        return h(
          NTag,
          {
            type: type.type,
            bordered: false,
            style: {
              padding: '2px 8px',
              fontWeight: 'bold'
            }
          },
          { default: () => type.text }
        )
      }
    },

    {
      title: '订单状态',
      key: 'orderStatus',
      width: 100,
      render(row) {
        return h(
          NTag,
          {
            type: orderStatusUtils.getType(row.orderStatus),
            bordered: false,
            style: {
              padding: '2px 8px',
              fontWeight: 'bold'
            }
          },
          { default: () => orderStatusUtils.getLabel(row.orderStatus) }
        )
      }
    },

    {
      title: '销售单位',
      key: 'salesOrgName',
      width: 200
    },

    {
      title: '销售顾问',
      key: 'salesAgentName',
      width: 100
    },

    {
      title: '客户名称',
      key: 'customerName',
      width: 120
    },

    {
      title: '联系电话',
      key: 'mobile',
      width: 120,
      render(row) {
        // 手机号脱敏处理：隐藏中间四位，以****代替
        if (!row.mobile) return '';
        if (row.mobile.length === 11) {
          return row.mobile.substring(0, 3) + '****' + row.mobile.substring(7);
        }
        return row.mobile;
      }
    },

    {
      title: '品牌',
      key: 'brand',
      width: 120
    },

    {
      title: '配置',
      key: 'configName',
      width: 200
    },

    {
      title: '成交金额(元)',
      key: 'dealAmount',
      width: 140,
      sorter: (a, b) => a.dealAmount - b.dealAmount,
      render(row) {
        // dealAmount单位是分，需要转换为元
        const amountInYuan = row.dealAmount / 100;
        // 使用toLocaleString格式化为千分位，保留2位小数
        const formattedAmount = amountInYuan.toLocaleString('zh-CN', {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2
        });
        return h('span', { style: { fontWeight: 'bold' } }, `¥${formattedAmount}`)
      }
    },

    {
      title: '操作',
      key: 'actions',
      width: 160,
      fixed: 'right',
      align: 'center',
      render: (row) => {
        const actions = [];

        // 查看按钮 - 所有状态都可以查看
        actions.push(
          h(
            'div',
            {
              style: {
                cursor: 'pointer',
                color: '#2080f0',
                fontSize: '20px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              },
              onClick: () => handleView(row.id),
              title: '查看详情'
            },
            [h(NIcon, { size: 20 }, { default: () => h(EyeOutlineIcon) })]
          )
        );

        // 编辑按钮 - 只有待处理、已确认状态可以编辑
        if (['pending', 'confirmed'].includes(row.orderStatus)) {
          actions.push(
            h(
              'div',
              {
                style: {
                  cursor: 'pointer',
                  color: '#18a058',
                  fontSize: '20px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                },
                onClick: () => handleEdit(row.id),
                title: '编辑订单'
              },
              [h(NIcon, { size: 20 }, { default: () => h(CreateOutlineIcon) })]
            )
          );
        }

        // 状态变更按钮 - 根据当前状态显示不同的操作
        switch (row.orderStatus) {
          case 'pending':
            // 待处理状态可以确认或取消
            actions.push(
              h(
                'div',
                {
                  style: {
                    cursor: 'pointer',
                    color: '#2080f0',
                    fontSize: '20px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  },
                  onClick: () => handleStatusChange(row.id, 'confirmed'),
                  title: '确认订单'
                },
                [h(NIcon, { size: 20 }, { default: () => h(CheckmarkCircleOutlineIcon) })]
              )
            );
            actions.push(
              h(
                'div',
                {
                  style: {
                    cursor: 'pointer',
                    color: '#d03050',
                    fontSize: '20px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  },
                  onClick: () => handleStatusChange(row.id, 'canceled'),
                  title: '取消订单'
                },
                [h(NIcon, { size: 20 }, { default: () => h(CloseCircleOutlineIcon) })]
              )
            );
            break;

          case 'confirmed':
            // 已确认状态可以交付或取消
            actions.push(
              h(
                'div',
                {
                  style: {
                    cursor: 'pointer',
                    color: '#18a058',
                    fontSize: '20px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  },
                  onClick: () => handleStatusChange(row.id, 'delivered'),
                  title: '标记为已交付'
                },
                [h(NIcon, { size: 20 }, { default: () => h(CarOutlineIcon) })]
              )
            );
            actions.push(
              h(
                'div',
                {
                  style: {
                    cursor: 'pointer',
                    color: '#d03050',
                    fontSize: '20px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  },
                  onClick: () => handleStatusChange(row.id, 'canceled'),
                  title: '取消订单'
                },
                [h(NIcon, { size: 20 }, { default: () => h(CloseCircleOutlineIcon) })]
              )
            );
            break;

          case 'delivered':
            // 已交付状态可以归档
            actions.push(
              h(
                'div',
                {
                  style: {
                    cursor: 'pointer',
                    color: '#909399',
                    fontSize: '20px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  },
                  onClick: () => handleStatusChange(row.id, 'archived'),
                  title: '归档订单'
                },
                [h(NIcon, { size: 20 }, { default: () => h(ArchiveOutlineIcon) })]
              )
            );
            break;
        }

        return h('div', {
          style: {
            display: 'flex',
            justifyContent: 'center',
            gap: '8px'
          }
        }, actions)
      }
    }
  ]
}
