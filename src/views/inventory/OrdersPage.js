import { ref, reactive, computed, onMounted, onUnmounted, markRaw } from 'vue'
import { useRouter } from 'vue-router'
import messages from '@/utils/messages'
import vehicleOrderApi from '@/api/vehicleOrder'
import {
  SearchOutline,
  RefreshOutline,
  AddOutline,
  EyeOutline,
  CreateOutline,
  CheckmarkCircleOutline,
  CloseCircleOutline,
  ArchiveOutline,
  CarOutline,
  CopyOutline
} from '@vicons/ionicons5'
import { dateRangeOptions, getDateRangeParams, handleDateRangeChange as handleDateChange, handleCustomDateChange as handleCustomDate } from '@/utils/dateRange'
import { createOrdersTableColumns } from './OrdersPageColumns.js'
import { vehicleBrandUtils, orderStatusUtils } from '@/utils/dictUtils'

// 组件导入
import OrderDetailModal from '@/components/orders/OrderDetailModal.vue'
import OrderEditModalNew from '@/components/orders/OrderEditModalNew.vue'
import DepartmentSelector from '@/components/users/DepartmentSelector.vue'

/**
 * 订单页面逻辑
 * @returns {Object} 页面所需的响应式数据和方法
 */
export function useOrdersPage() {
  // 使用 markRaw 包装图标组件，防止它们被 Vue 的响应式系统处理
  const SearchOutlineIcon = markRaw(SearchOutline)
  const RefreshOutlineIcon = markRaw(RefreshOutline)
  const AddOutlineIcon = markRaw(AddOutline)
  const EyeOutlineIcon = markRaw(EyeOutline)
  const CreateOutlineIcon = markRaw(CreateOutline)
  const CheckmarkCircleOutlineIcon = markRaw(CheckmarkCircleOutline)
  const CloseCircleOutlineIcon = markRaw(CloseCircleOutline)
  const ArchiveOutlineIcon = markRaw(ArchiveOutline)
  const CarOutlineIcon = markRaw(CarOutline)
  const CopyOutlineIcon = markRaw(CopyOutline)

  // 路由
  const router = useRouter()

  // 状态变量
  const tableRef = ref(null)
  const orderEditModalRef = ref(null)
  const loading = ref(false)
  const dialogVisible = ref(false)
  const dialogTitle = ref('新增订单')
  const isEdit = ref(false)
  const selectedRows = ref([])

  /**
   * 车辆类别选项 - 从字典数据获取
   */
  const vehicleCategoryOptions = computed(() => vehicleBrandUtils.getOptions())

  /**
   * 订单状态选项 - 从字典数据获取
   */
  const orderStatusOptions = computed(() => orderStatusUtils.getOptions())

  // 筛选表单
  const filterForm = reactive({
    dateRange: null,
    customDateRange: null,
    vehicleCategory: null,
    invoiceOrg: null,
    minAmount: null,
    maxAmount: null,
    keywords: '',
    orderStatus: null
  })

  // 数据列表
  const ordersData = ref([])

  // 窗口高度响应式变量
  const windowHeight = ref(window.innerHeight)

  // 计算表格最大高度 - 用于虚拟滚动（无页面滚动模式）
  const tableMaxHeight = computed(() => {
    // 计算可用高度：视窗高度 - 页面padding - 筛选区域 - 工具栏 - 分页区域
    const pagepadding = 32 // 页面上下padding
    const filterHeight = 120 // 筛选区域高度
    const toolbarHeight = 60 // 工具栏高度
    const paginationHeight = 50 // 分页区域高度
    const margin = 16 // 额外边距

    const calculatedHeight =
      windowHeight.value -
      pagepadding -
      filterHeight -
      toolbarHeight -
      paginationHeight -
      margin

    // 在无页面滚动模式下，直接使用计算出的高度
    // 确保表格能够充分利用可用空间
    const maxHeight = Math.max(calculatedHeight, 300)

    // 在开发环境中输出调试信息
    if (process.env.NODE_ENV === 'development') {
      console.log('表格高度计算 (无页面滚动):', {
        windowHeight: windowHeight.value,
        calculatedHeight,
        maxHeight,
        components: {
          pagepadding,
          filterHeight,
          toolbarHeight,
          paginationHeight,
          margin
        }
      })
    }

    return maxHeight
  })

  // 分页配置
  const pagination = reactive({
    page: 1,
    pageSize: 100, // 增加默认页面大小，确保有足够数据触发虚拟滚动
    pageCount: 1,
    showSizePicker: true,
    pageSizes: [50, 100, 200],
    itemCount: 0,
    showQuickJumper: false
  })

  /**
   * 复制文本到剪贴板
   * @param {string} text - 要复制的文本
   */
  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text)
      .then(() => {
        messages.success('已复制到剪贴板')
      })
      .catch(err => {
        console.error('复制失败:', err)
        messages.error('复制失败')
      })
  }

  /**
   * 处理查看订单详情
   * @param {number} id - 订单ID
   */
  const handleView = (id) => {
    currentDetailId.value = id
    detailDialogVisible.value = true
  }

  /**
   * 处理编辑订单
   * @param {number} id - 订单ID
   */
  const handleEdit = async (id) => {
    try {
      loading.value = true

      // 调用API获取详细数据
      const response = await vehicleOrderApi.getOrderDetail(id)

      if (response.code === 200) {
        const apiData = response.data

        isEdit.value = true

        // 如果是定金订单，导航到定金订单页面
        if (apiData.orderType === 'deposit') {
          messages.info('定金订单请在定金订单管理页面编辑')
          router.push('/inventory/deposit-order')
          return
        }

        // 普通销售订单
        dialogTitle.value = '编辑销售订单'

        // 设置表单数据
        if (orderEditModalRef.value) {
          orderEditModalRef.value.setFormData(apiData)
        }

        dialogVisible.value = true
      } else {
        messages.error(response.message || '获取订单数据失败')
      }
    } catch (error) {
      console.error('获取订单数据失败:', error)
      messages.error('获取订单数据失败，请稍后重试')
    } finally {
      loading.value = false
    }
  }

  /**
   * 处理订单状态变更
   * @param {number} id - 订单ID
   * @param {string} newStatus - 新状态
   */
  const handleStatusChange = async (id, newStatus) => {
    try {
      loading.value = true

      // 调用API更新订单状态
      const response = await vehicleOrderApi.updateOrderStatus(id, newStatus)

      if (response.code === 200) {
        messages.success(`订单状态已更新为${getStatusText(newStatus)}`)
        // 刷新数据列表
        refreshData()
      } else {
        messages.error(response.message || '更新订单状态失败')
      }
    } catch (error) {
      console.error('更新订单状态失败:', error)
      messages.error('更新订单状态失败，请稍后重试')
    } finally {
      loading.value = false
    }
  }

  /**
   * 获取状态文本 - 从字典数据获取
   * @param {string} status - 状态值
   * @returns {string} 状态文本
   */
  const getStatusText = (status) => orderStatusUtils.getLabel(status)

  /**
   * 刷新数据列表
   */
  const refreshData = async () => {
    loading.value = true
    try {
      // 构建查询参数
      const params = {
        page: pagination.page,
        size: pagination.pageSize
      }

      // 添加筛选条件
      if (filterForm.keywords) {
        params.keywords = filterForm.keywords
      }

      // 处理日期范围
      if (filterForm.dateRange) {
        const dateRange = getDateRangeParams(filterForm.dateRange, filterForm.customDateRange)
        if (dateRange.startDate) params.startDate = dateRange.startDate
        if (dateRange.endDate) params.endDate = dateRange.endDate
      }

      // 处理车辆类别
      if (filterForm.vehicleCategory) {
        params.brand = filterForm.vehicleCategory
      }

      // 处理销售单位
      if (filterForm.invoiceOrg) {
        params.salesOrgId = filterForm.invoiceOrg.id
      }

      // 处理订单状态
      if (filterForm.orderStatus) {
        params.orderStatus = filterForm.orderStatus
      }

      // 调用API获取数据
      const response = await vehicleOrderApi.getOrderList(params)

      if (response.code === 200) {
        // 直接使用返回的数据列表
        ordersData.value = response.data.list

        // 更新分页信息
        pagination.itemCount = response.data.total
        pagination.pageCount = response.data.pages
      } else {
        messages.error(response.message || '数据加载失败')
      }
    } catch (error) {
      console.error('获取订单列表失败:', error)
      messages.error('加载数据失败，请稍后重试')
    } finally {
      loading.value = false
    }
  }

  /**
   * 处理日期范围变化
   * @param {string} value - 日期范围值
   */
  const handleDateRangeChange = (value) => {
    handleDateChange(value, filterForm, handleSearch)
  }

  /**
   * 处理自定义日期变化
   * @param {Array} dates - 日期数组
   */
  const handleCustomDateChange = (dates) => {
    handleCustomDate(dates, handleSearch)
  }

  /**
   * 处理查询
   */
  const handleSearch = () => {
    pagination.page = 1
    refreshData()
  }

  /**
   * 显示新增销售订单对话框
   */
  const showAddDialog = () => {
    // 重置表单
    if (orderEditModalRef.value) {
      orderEditModalRef.value.resetForm()
    }

    // 打开编辑弹窗
    isEdit.value = false
    dialogTitle.value = '新增销售订单'
    dialogVisible.value = true
  }

  /**
   * 导航到定金订单页面
   */
  const navigateToDepositOrderPage = () => {
    router.push('/inventory/deposit-order')
  }

  /**
   * 处理保存成功
   */
  const handleSaveSuccess = () => {
    refreshData() // 刷新数据列表
  }

  /**
   * 处理选择变化
   * @param {Array} keys - 选中的行键
   */
  const handleSelectionChange = (keys) => {
    selectedRows.value = ordersData.value.filter(item => keys.includes(item.id))
  }

  /**
   * 处理页码变化
   * @param {number} page - 页码
   */
  const handlePageChange = (page) => {
    pagination.page = page
    refreshData()
  }

  /**
   * 处理页面大小变化
   * @param {number} pageSize - 页面大小
   */
  const handlePageSizeChange = (pageSize) => {
    pagination.pageSize = pageSize
    pagination.page = 1
    refreshData()
  }

  /**
   * 处理导入成功
   * @param {Object} fileInfo - 文件信息
   */
  const handleImportSuccess = async (fileInfo) => {
    try {
      messages.success(`文件上传成功: ${fileInfo.fileName}`)
      console.log('文件上传路径:', fileInfo.fileKey)

      // 创建FormData对象
      const formData = new FormData();
      formData.append('file', fileInfo.file);

      // 调用订单导入接口
      const response = await vehicleOrderApi.importOrders(formData)

      if (response.code === 200) {
        messages.success('文件解析成功')
        // 刷新数据列表
        refreshData()
      } else {
        messages.error(response.message || '文件解析失败')
      }
    } catch (error) {
      console.error('文件解析失败:', error)
      messages.error('文件解析失败，请检查文件格式是否正确')
    }
  }

  /**
   * 处理导入错误
   * @param {string} errorMsg - 错误信息
   */
  const handleImportError = (errorMsg) => {
    console.error(`导入失败: ${errorMsg}`)
  }

  // 详情弹窗
  const detailDialogVisible = ref(false)
  const currentDetailId = ref(null)

  // 表格数据
  const filteredData = computed(() => {
    return ordersData.value
  })

  // 表格列配置
  const columns = computed(() => {
    const icons = {
      CopyOutlineIcon,
      EyeOutlineIcon,
      CreateOutlineIcon,
      CheckmarkCircleOutlineIcon,
      CloseCircleOutlineIcon,
      ArchiveOutlineIcon,
      CarOutlineIcon
    }

    const handlers = {
      copyToClipboard,
      handleView,
      handleEdit,
      handleStatusChange
    }

    return createOrdersTableColumns(icons, handlers)
  })

  // 动态计算表格横向滚动宽度
  const scrollX = computed(() => {
    let totalWidth = 0
    const columnDetails = []

    columns.value.forEach((column, index) => {
      if (column.width) {
        // 如果宽度是'auto'，使用minWidth或默认值来计算
        const width = column.width === 'auto' ? (column.minWidth || 200) : column.width
        totalWidth += width
        columnDetails.push({
          index,
          title: column.title,
          width: column.width,
          actualWidth: width,
          fixed: column.fixed
        })
      } else {
        columnDetails.push({
          index,
          title: column.title,
          width: 'undefined',
          fixed: column.fixed
        })
      }
    })

    // 检查屏幕宽度，如果屏幕足够宽，则不限制scroll-x
    const screenWidth = window.innerWidth
    const shouldUseScrollX = screenWidth <= totalWidth + 200 // 给200px的缓冲区

    // 在开发环境中输出详细调试信息
    if (process.env.NODE_ENV === 'development') {
      console.log('表格scrollX计算详情:', {
        totalWidth,
        screenWidth,
        shouldUseScrollX,
        finalScrollX: shouldUseScrollX ? totalWidth : undefined,
        columnsCount: columns.value.length,
        columnsWithWidth: columns.value.filter(col => col.width).length,
        columnDetails
      })
    }

    // 如果屏幕宽度足够，不设置scroll-x限制，让表格自适应填充
    return shouldUseScrollX ? totalWidth : undefined
  })

  // 窗口大小变化监听器
  const handleResize = () => {
    windowHeight.value = window.innerHeight
    // 在开发环境中输出调试信息
    if (process.env.NODE_ENV === 'development') {
      console.log('窗口高度变化 (无页面滚动):', {
        windowHeight: windowHeight.value,
        tableMaxHeight: tableMaxHeight.value,
        calculatedHeight: windowHeight.value - 32 - 120 - 60 - 50 - 16
      })
    }
  }

  // 初始化
  onMounted(() => {
    refreshData()
    window.addEventListener('resize', handleResize)

    // 在开发环境中输出初始调试信息
    if (process.env.NODE_ENV === 'development') {
      console.log('OrdersPage 初始化 (无页面滚动模式):', {
        windowHeight: windowHeight.value,
        tableMaxHeight: tableMaxHeight.value,
        calculatedHeight: windowHeight.value - 32 - 120 - 60 - 50 - 16,
        mode: '表格内部虚拟滚动'
      })
    }
  })

  // 清理
  onUnmounted(() => {
    window.removeEventListener('resize', handleResize)
  })

  return {
    // 图标
    SearchOutlineIcon,
    RefreshOutlineIcon,
    AddOutlineIcon,
    EyeOutlineIcon,
    CreateOutlineIcon,
    CheckmarkCircleOutlineIcon,
    CloseCircleOutlineIcon,
    ArchiveOutlineIcon,
    CarOutlineIcon,
    CopyOutlineIcon,

    // 组件
    OrderDetailModal,
    OrderEditModalNew,
    DepartmentSelector,

    // 路由
    router,

    // 状态
    tableRef,
    orderEditModalRef,
    loading,
    dialogVisible,
    dialogTitle,
    isEdit,
    selectedRows,
    detailDialogVisible,
    currentDetailId,

    // 数据
    dateRangeOptions,
    vehicleCategoryOptions,
    orderStatusOptions,
    filterForm,
    ordersData,
    pagination,
    filteredData,
    columns,
    scrollX,
    tableMaxHeight,
    windowHeight,

    // 方法
    refreshData,
    handleDateRangeChange,
    handleCustomDateChange,
    handleSearch,
    showAddDialog,
    navigateToDepositOrderPage,
    copyToClipboard,
    handleView,
    handleEdit,
    handleSaveSuccess,
    handleSelectionChange,
    handlePageChange,
    handlePageSizeChange,
    handleImportSuccess,
    handleImportError,
    handleStatusChange,
    getStatusText
  }
}
